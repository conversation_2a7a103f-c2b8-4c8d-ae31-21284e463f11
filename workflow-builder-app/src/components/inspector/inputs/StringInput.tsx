import React from "react";
import { InputDefinition } from "@/types";
import { ValidationWrapper } from "../ValidationWrapper";
import { Input } from "@/components/ui/input";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import { TemplateVariableInput } from "@/components/ui/template-variable-input";
import { TemplateVariableDisplay } from "@/components/ui/template-variable-display";
import { cn } from "@/lib/utils";
import { getHtmlInputType } from "@/utils/valueFormatting";
import { hasTemplateVariables } from "@/utils/templateVariables";

interface StringInputProps {
  inputDef: InputDefinition;
  value: string;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId?: string;
}

/**
 * Component for rendering string inputs
 */
export function StringInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: StringInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;
  const stringValue = value ?? "";
  const hasTemplateVars = hasTemplateVariables(stringValue);

  return (
    <ValidationWrapper inputDef={inputDef} value={value} enableTemplateVariableValidation={true}>
      <div className="relative space-y-2">
        {/* Enhanced Template Variable Display for read-only/connected inputs */}
        {(isDisabled || isConnected) && hasTemplateVars && (
          <TemplateVariableDisplay
            value={stringValue}
            showHighlighting={true}
            showPreview={true}
            showIndicators={!isConnected} // Hide indicators when connected to reduce clutter
            maxLines={1}
            compact={true}
            className="mb-1"
            availableVariables={[]} // TODO: Connect to workflow context for available variables
          />
        )}

        {/* Input field - hidden when connected and has template variables */}
        {!(isConnected && hasTemplateVars) && (
          <>
            {hasTemplateVars || inputDef.input_type === 'template' ? (
              // Use enhanced template variable input for strings with template variables
              <TemplateVariableInput
                id={inputId}
                value={stringValue}
                onChange={(newValue) => onChange(inputDef.name, newValue)}
                placeholder={inputDef.display_name}
                disabled={isDisabled}
                className={cn(
                  "bg-background/50 mt-1 h-8 text-xs",
                  isDisabled && "opacity-50"
                )}
                showValidation={false} // Validation handled by ValidationWrapper
                showSuggestions={true}
                availableVariables={[]} // TODO: Connect to workflow context for available variables
              />
            ) : (
              // Use standard input for regular strings
              <Input
                id={inputId}
                type={getHtmlInputType(inputDef.input_type)}
                value={stringValue}
                onChange={(e) => onChange(inputDef.name, e.target.value)}
                placeholder={inputDef.display_name}
                className={cn(
                  "bg-background/50 mt-1 h-8 text-xs",
                  isDisabled && "opacity-50"
                )}
                disabled={isDisabled}
              />
            )}
          </>
        )}
        {isDisabled && isConnected && <ConnectedIndicator />}
      </div>
    </ValidationWrapper>
  );
}
