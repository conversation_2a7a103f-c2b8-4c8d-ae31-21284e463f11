import React from "react";
import { InputDefinition } from "@/types";
import { ValidationWrapper } from "../ValidationWrapper";
import { Input } from "@/components/ui/input";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import { TemplateVariableInput } from "@/components/ui/template-variable-input";
import { cn } from "@/lib/utils";
import { getHtmlInputType } from "@/utils/valueFormatting";
import { hasTemplateVariables } from "@/utils/templateVariables";

interface StringInputProps {
  inputDef: InputDefinition;
  value: string;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId?: string;
}

/**
 * Component for rendering string inputs
 */
export function StringInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: StringInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;
  const stringValue = value ?? "";
  const hasTemplateVars = hasTemplateVariables(stringValue);

  return (
    <ValidationWrapper inputDef={inputDef} value={value} enableTemplateVariableValidation={true}>
      <div className="relative">
        {hasTemplateVars || inputDef.input_type === 'template' ? (
          // Use enhanced template variable input for strings with template variables
          <TemplateVariableInput
            id={inputId}
            value={stringValue}
            onChange={(newValue) => onChange(inputDef.name, newValue)}
            placeholder={inputDef.display_name}
            disabled={isDisabled}
            className={cn(
              "bg-background/50 mt-1 h-8 text-xs",
              isDisabled && "opacity-50"
            )}
            showValidation={false} // Validation handled by ValidationWrapper
            showSuggestions={true}
            availableVariables={[]} // TODO: Connect to workflow context for available variables
          />
        ) : (
          // Use standard input for regular strings
          <Input
            id={inputId}
            type={getHtmlInputType(inputDef.input_type)}
            value={stringValue}
            onChange={(e) => onChange(inputDef.name, e.target.value)}
            placeholder={inputDef.display_name}
            className={cn(
              "bg-background/50 mt-1 h-8 text-xs",
              isDisabled && "opacity-50"
            )}
            disabled={isDisabled}
          />
        )}
        {isDisabled && isConnected && <ConnectedIndicator />}
      </div>
    </ValidationWrapper>
  );
}
